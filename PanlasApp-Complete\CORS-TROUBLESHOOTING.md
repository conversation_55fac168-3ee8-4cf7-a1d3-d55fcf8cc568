# CORS Troubleshooting Guide

## Overview
This guide helps you resolve Cross-Origin Resource Sharing (CORS) issues in the PanlasApp project.

## What I Fixed

### 1. Backend CORS Configuration
- ✅ Added missing `CORS_ORIGIN` environment variable
- ✅ Enhanced CORS options with better logging
- ✅ Added support for both localhost and 127.0.0.1
- ✅ Added preflight OPTIONS handler
- ✅ Expanded allowed headers list
- ✅ Added CORS test endpoint

### 2. Frontend API URLs
- ✅ Created `.env.local` file with proper API URL
- ✅ Fixed hardcoded URLs in multiple components:
  - Home.jsx
  - RecommendedMeals.jsx
  - AdminDashboard.jsx
  - FeedbackManagement.jsx
  - UserActivityLog.jsx
  - SystemHealth.jsx
  - HelpCenter.jsx
  - userAPI.js

### 3. Mobile App Configuration
- ✅ Made IP address configuration more flexible
- ✅ Added IP detection helper function

## Testing CORS

### Method 1: Use the Test Page
1. Start your backend server:
   ```bash
   cd "PanlasApp Website/panlasapp web/meal-planner-backend"
   npm start
   ```

2. Open the test page in your browser:
   ```
   file:///path/to/panlas-new/PanlasApp-Complete/PanlasApp Website/panlasapp web/test-cors.html
   ```

3. Click "Test CORS" and "Test API Endpoint" buttons

### Method 2: Browser Developer Tools
1. Open your frontend application
2. Open browser Developer Tools (F12)
3. Check the Console tab for CORS errors
4. Check the Network tab to see failed requests

### Method 3: Command Line Test
```bash
# Test CORS preflight
curl -X OPTIONS http://localhost:5000/api/cors-test \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -v

# Test actual request
curl -X GET http://localhost:5000/api/cors-test \
  -H "Origin: http://localhost:3000" \
  -H "Content-Type: application/json" \
  -v
```

## Common CORS Errors and Solutions

### Error: "Access to fetch at '...' from origin '...' has been blocked by CORS policy"

**Solution:**
1. Ensure backend server is running
2. Check that `CORS_ORIGIN` in backend `.env` matches your frontend URL
3. Verify frontend is using the correct API URL

### Error: "CORS policy: No 'Access-Control-Allow-Origin' header"

**Solution:**
1. Check backend CORS configuration
2. Ensure the origin is in the allowed origins list
3. Check server logs for CORS debug messages

### Error: "CORS policy: Request header field authorization is not allowed"

**Solution:**
1. Add missing headers to `allowedHeaders` in server.js
2. Ensure preflight requests are handled properly

## Environment Variables

### Backend (.env)
```
NODE_ENV=development
MONGODB_URI=your_mongodb_uri
JWT_SECRET=your_jwt_secret
PORT=5000
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000
```

### Frontend (.env.local)
```
VITE_API_URL=http://localhost:5000/api
VITE_NODE_ENV=development
```

## Mobile App Configuration

Update the IP address in `panlas-mobile-app/src/services/api.js`:
```javascript
const getLocalIP = () => {
  const commonIPs = [
    'YOUR_COMPUTER_IP',  // Replace with your actual IP
    '*************',     // Example
    '********',          // Android emulator
    'localhost'          // Fallback
  ];
  return commonIPs[0];
};
```

## Debugging Steps

1. **Check Server Logs**: Look for CORS debug messages in the backend console
2. **Verify Environment Variables**: Ensure all required env vars are set
3. **Test with Postman**: API should work without CORS issues
4. **Check Network Tab**: Look for failed preflight OPTIONS requests
5. **Verify URLs**: Ensure frontend is calling the correct backend URL

## Production Deployment

For production, update environment variables:

### Backend (Railway/Heroku)
```
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.com
CORS_ORIGIN=https://your-frontend-domain.com
```

### Frontend (Vercel/Netlify)
```
VITE_API_URL=https://your-backend-domain.com/api
```

## Need Help?

If you're still experiencing CORS issues:
1. Check the browser console for specific error messages
2. Verify all environment variables are correctly set
3. Test the CORS endpoint: `GET /api/cors-test`
4. Check that both frontend and backend are running on expected ports
