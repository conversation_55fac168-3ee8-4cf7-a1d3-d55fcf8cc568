import { useState, useEffect, useMemo } from "react";
import Header from "../Header/Header";
import Sidebar from "../Sidebar/Sidebar";
import { FaHeart, FaRegHeart, FaTimes, FaChevronDown } from "react-icons/fa";
import "../../../src/App.css";
import axios from "axios";
import { useFavorites } from "../../components/Favorites/FavoritesContext";
import userAPI from "../../services/userAPI";
import RecommendedMeals from "../RecommendedMeals/RecommendedMeals";
import analyticsService from "../../services/analyticsService";
import FloatingChatButton from "../FloatingChatButton/FloatingChatButton";

//Recently viewed Funtion
function addRecentlyViewedMeal(meal) {
  let recent = JSON.parse(localStorage.getItem(RECENTLY_VIEWED_KEY)) || [];
  // Remove if already exists
  recent = recent.filter((m) => (m.id || m._id) !== (meal.id || meal._id));
  // Add to front
  recent.unshift(meal);
  // Limit to MAX_RECENT
  if (recent.length > MAX_RECENT) recent = recent.slice(0, MAX_RECENT);
  localStorage.setItem(RECENTLY_VIEWED_KEY, JSON.stringify(recent));
}

const Home = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [activeFilter, setActiveFilter] = useState("All");
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [priceRange, setPriceRange] = useState("All");
  const [showRangeDropdown, setShowRangeDropdown] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [displayedCategories, setDisplayedCategories] = useState([]);
  const [hiddenCategories, setHiddenCategories] = useState([]);
  const [notification, setNotification] = useState({show: false,message: "",});
  const [filipinoMealData, setFilipinoMealData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userDishes, setUserDishes] = useState([]);
  const [userPreferences, setUserPreferences] = useState({});

  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchError, setSearchError] = useState(null);

  // Favorites context
  const { favorites, addFavorite, removeFavorite, isFavorite } = useFavorites();

  //History context
  const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";
  const MAX_RECENT = 10; // Show up to 10 recent meals

  // Load user dietary preferences
  const loadUserPreferences = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        const response = await userAPI.getDietaryPreferences();
        console.log('Loaded user preferences:', response);

        if (response.success && response.dietaryPreferences) {
          console.log('Setting user preferences to:', response.dietaryPreferences);
          setUserPreferences(response.dietaryPreferences);
        } else {
          console.log('No dietary preferences found, setting empty object');
          setUserPreferences({});
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      setUserPreferences({});
    }
  };

  // Fetch meal data from API
  useEffect(() => {
    const fetchMealData = async () => {
      try {
        setLoading(true);
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const data = await axios.get(`${API_BASE_URL}/meals/filipino`);

        // Log sample meal data to understand structure
        if (data.data && data.data.length > 0) {
          console.log('📊 Sample meal data structure:', {
            name: data.data[0].name,
            dietType: data.data[0].dietType,
            dietaryAttributes: data.data[0].dietaryAttributes,
            dietaryTags: data.data[0].dietaryTags,
            ingredients: data.data[0].ingredients?.slice(0, 3),
            allergens: data.data[0].allergens
          });
        }

        setUserDishes(data.data);
        setFilipinoMealData(data.data);
        await loadUserPreferences(); // Load preferences after meals
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };
    fetchMealData();
  }, []);

  // Search API effect
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setSearchResults([]);
      setSearchLoading(false);
      setSearchError(null);
      return;
    }
    setSearchLoading(true);
    setSearchError(null);
    const delayDebounce = setTimeout(() => {
      axios
        .get("http://localhost:5000/api/meals/filipino", {
          params: { search: searchTerm },
        })
        .then((res) => {
          setSearchResults(res.data);
          setSearchLoading(false);

          // Track search analytics
          analyticsService.trackSearch(searchTerm, {
            resultsCount: res.data.length,
            hasResults: res.data.length > 0
          });
        })
        .catch(() => {
          setSearchError("Error fetching search results");
          setSearchLoading(false);
        });
    }, 400);
    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  // Add price range to each meal - with safety check
  const mealsWithPriceRange = useMemo(() => {
    const getPriceRange = (calories) => {
      if (calories < 300) return "Low";
      if (calories < 450) return "Mid";
      return "High";
    };
    return filipinoMealData
      ? filipinoMealData.map((meal) => ({
          ...meal,
          priceRange: getPriceRange(meal.calories),
        }))
      : [];
  }, [filipinoMealData]);

  // Convert price range to peso signs
  const getPesoSigns = (priceRange) => {
    switch (priceRange) {
      case "Low": return "₱";
      case "Mid": return "₱₱";
      case "High": return "₱₱₱";
      default: return "₱";
    }
  };

  // Create main dishes and recommended dishes
  const allDishes = mealsWithPriceRange.slice(
    0,
    Math.min(30, mealsWithPriceRange.length)
  );
  const recommended = mealsWithPriceRange.slice(
    Math.min(30, mealsWithPriceRange.length),
    mealsWithPriceRange.length
  );

  // Get unique categories from the data
  const categories = useMemo(() => {
    const allCategories = mealsWithPriceRange.flatMap((dish) =>
      Array.isArray(dish.category) ? dish.category : [dish.category]
    );
    return [
      "All",
      ...Array.from(
        new Set(
          allCategories.map(
            (c) => c && c.charAt(0).toUpperCase() + c.slice(1)
          )
        )
      ),
    ];
  }, [mealsWithPriceRange]);

  // Determine which categories to show directly and which to put in dropdown
  useEffect(() => {
    const MAX_VISIBLE_CATEGORIES = 5;
    if (categories.length <= MAX_VISIBLE_CATEGORIES) {
      setDisplayedCategories(categories);
      setHiddenCategories([]);
    } else {
      setDisplayedCategories(categories.slice(0, MAX_VISIBLE_CATEGORIES));
      setHiddenCategories(categories.slice(MAX_VISIBLE_CATEGORIES));
    }
  }, [categories]);

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const toggleFavorite = (e, dish) => {
    e.stopPropagation();
    const dishId = dish.id || dish._id;
    if (isFavorite(dishId)) {
      removeFavorite(dishId);
      showNotification("Removed from favorites");
    } else {
      addFavorite(dish);
      showNotification("Added to favorites");
    }
  };

  const showNotification = (message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 3000);
  };

const openMealDetails = (dish) => {
  setSelectedMeal(dish);
  setShowModal(true);

  // Track meal view analytics
  analyticsService.trackMealView(dish.id || dish._id, dish.name);

  // Add to recently viewed on backend
  const token = localStorage.getItem("token");
  if (token) {
    axios.post(
      "http://localhost:5000/api/users/recently-viewed-meals",
      { meal: dish },
      { headers: { Authorization: `Bearer ${token}` } }
    ).catch((err) => {
      // handle error if needed
    });
  }
};

  const closeMealDetails = () => {
    setShowModal(false);
  };

  const toggleRangeDropdown = () => {
    setShowRangeDropdown(!showRangeDropdown);
    if (showCategoryDropdown) setShowCategoryDropdown(false);
  };

  const toggleCategoryDropdown = () => {
    setShowCategoryDropdown(!showCategoryDropdown);
    if (showRangeDropdown) setShowRangeDropdown(false);
  };

  const selectPriceRange = (range) => {
    setPriceRange(range);
    setShowRangeDropdown(false);

    // Track filter analytics
    analyticsService.trackEvent('filter_apply', {
      filterType: 'priceRange',
      filterValue: range
    });
  };

  const selectCategory = (category) => {
    setActiveFilter(category);
    setShowCategoryDropdown(false);

    // Track filter analytics
    analyticsService.trackEvent('filter_apply', {
      filterType: 'category',
      filterValue: category
    });
  };

  // Filter meals by dietary preferences (matching mobile app logic)
  const filterMealsByDietaryPreferences = (meals) => {
    let filtered = meals;

    console.log('🔍 Filtering meals with preferences:', userPreferences);
    console.log('📊 Total meals before filtering:', meals.length);

    // Filter by user dietary preferences first
    if (userPreferences && Object.keys(userPreferences).length > 0) {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};
        const dietaryTags = meal.dietaryTags || [];
        const ingredients = meal.ingredients || [];
        const mealName = meal.name || '';

        console.log(`🔍 Checking meal "${mealName}":`, {
          dietType,
          dietaryTags,
          ingredients: ingredients.slice(0, 3) // Show first 3 ingredients
        });

        // Check dietary restrictions
        if (userPreferences.restrictions && userPreferences.restrictions.length > 0) {
          const meetsRestrictions = userPreferences.restrictions.every(restriction => {
            let meets = false;

            switch(restriction) {
              case 'Vegetarian':
                // Check boolean flag first, then fallback to tags and ingredients
                meets = dietType.isVegetarian === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('vegetarian')) ||
                        !ingredients.some(ing =>
                          ['pork', 'beef', 'chicken', 'fish', 'meat', 'seafood', 'shrimp', 'crab'].some(meat =>
                            ing.toLowerCase().includes(meat)
                          )
                        );
                break;
              case 'Vegan':
                meets = dietType.isVegan === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('vegan')) ||
                        (!ingredients.some(ing =>
                          ['pork', 'beef', 'chicken', 'fish', 'meat', 'seafood', 'dairy', 'milk', 'cheese', 'egg', 'butter'].some(animal =>
                            ing.toLowerCase().includes(animal)
                          )
                        ));
                break;
              case 'Gluten-Free':
                meets = dietType.isGlutenFree === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('gluten-free')) ||
                        !ingredients.some(ing =>
                          ['wheat', 'flour', 'bread', 'pasta', 'noodles', 'soy sauce'].some(gluten =>
                            ing.toLowerCase().includes(gluten)
                          )
                        );
                break;
              case 'Dairy-Free':
                meets = dietType.isDairyFree === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('dairy-free')) ||
                        !ingredients.some(ing =>
                          ['milk', 'cheese', 'butter', 'cream', 'yogurt', 'dairy'].some(dairy =>
                            ing.toLowerCase().includes(dairy)
                          )
                        );
                break;
              case 'Nut-Free':
                meets = dietType.isNutFree === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('nut-free')) ||
                        !ingredients.some(ing =>
                          ['peanut', 'almond', 'walnut', 'cashew', 'pecan', 'hazelnut', 'pistachio', 'nuts'].some(nut =>
                            ing.toLowerCase().includes(nut)
                          )
                        );
                break;
              case 'Low-Carb':
                meets = dietType.isLowCarb === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('low-carb')) ||
                        (meal.carbs && meal.carbs < 20); // Less than 20g carbs
                break;
              case 'Keto':
                meets = dietType.isKeto === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('keto')) ||
                        (meal.carbs && meal.carbs < 10 && meal.fat && meal.fat > 15); // Very low carb, high fat
                break;
              case 'Pescatarian':
                meets = dietType.isPescatarian === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('pescatarian')) ||
                        !ingredients.some(ing =>
                          ['pork', 'beef', 'chicken', 'meat', 'lamb', 'turkey'].some(meat =>
                            ing.toLowerCase().includes(meat)
                          )
                        );
                break;
              case 'Halal':
                // For Halal, we need to be very strict - only allow if explicitly marked as Halal
                // or if it doesn't contain any non-Halal ingredients
                meets = dietType.isHalal === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('halal')) ||
                        (!ingredients.some(ing =>
                          ['pork', 'ham', 'bacon', 'alcohol', 'wine', 'beer', 'gelatin'].some(nonHalal =>
                            ing.toLowerCase().includes(nonHalal)
                          )
                        ) && !mealName.toLowerCase().includes('pork'));
                break;
              default:
                meets = true;
            }

            if (!meets) {
              console.log(`❌ Meal "${mealName}" doesn't meet restriction: ${restriction}`);
            }
            return meets;
          });
          if (!meetsRestrictions) return false;
        }

        // Check allergies - exclude meals with allergens
        if (userPreferences.allergies && userPreferences.allergies.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasAllergen = userPreferences.allergies.some(allergy =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(allergy.toLowerCase())
            )
          );
          if (hasAllergen) {
            console.log(`❌ Meal "${meal.name}" contains allergen "${userPreferences.allergies.join(', ')}" in ingredients: ${ingredients.join(', ')}`);
            return false;
          }
        }

        // Check disliked ingredients
        if (userPreferences.dislikedIngredients && userPreferences.dislikedIngredients.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasDislikedIngredient = userPreferences.dislikedIngredients.some(disliked =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(disliked.toLowerCase())
            )
          );
          if (hasDislikedIngredient) {
            console.log(`❌ Meal "${meal.name}" contains disliked ingredient "${userPreferences.dislikedIngredients.join(', ')}" in ingredients: ${ingredients.join(', ')}`);
            return false;
          }
        }

        return true;
      });
    }

    console.log('📊 Total meals after dietary filtering:', filtered.length);
    return filtered;
  };

  // Filter dishes based on the active filter, price range, and dietary preferences
  const filterDishes = (dishes) => {
    let filtered = dishes;

    // Apply dietary preferences first
    filtered = filterMealsByDietaryPreferences(filtered);

    // Then apply category filter
    if (activeFilter !== "All") {
      filtered = filtered.filter((dish) =>
        Array.isArray(dish.category)
          ? dish.category
              .map((c) => c.toLowerCase())
              .includes(activeFilter.toLowerCase())
          : (dish.category || "").toLowerCase() === activeFilter.toLowerCase()
      );
    }

    // Finally apply price range filter
    if (priceRange !== "All") {
      filtered = filtered.filter((dish) => dish.priceRange === priceRange);
    }

    return filtered;
  };

  const filteredDishes = filterDishes(allDishes);
  const filteredRecommended = filterDishes(recommended);

  // Use searchResults if searching, otherwise use filteredDishes
  const dishesToShow = searchTerm.trim()
    ? searchResults.map((meal) => ({
        ...meal,
        priceRange:
          meal.calories < 300
            ? "Low"
            : meal.calories < 450
            ? "Mid"
            : "High",
      }))
    : filteredDishes;


  // If data is loading, show a loading message
  if (loading) {
    return (
      <div className="app-container">
        <Header
          toggleSidebar={toggleSidebar}
          openMealDetails={openMealDetails}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="container">
              <h1>Loading Filipino meals...</h1>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If there was an error loading data
  if (error) {
    return (
      <div className="app-container">
        <Header
          toggleSidebar={toggleSidebar}
          openMealDetails={openMealDetails}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="container">
              <h1>Error</h1>
              <p>{error}</p>
              <button onClick={() => window.location.reload()}>
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If data isn't loaded yet, show a loading message
  if (!filipinoMealData || filipinoMealData.length === 0) {
    return (
      <div className="app-container">
        <Header
          toggleSidebar={toggleSidebar}
          openMealDetails={openMealDetails}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="container">
              <h1>No Filipino meals found</h1>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container">
      <Header
        toggleSidebar={toggleSidebar}
        openMealDetails={openMealDetails}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        <div className="main-content">
          <div className="container">
            <h1>HOME</h1>
            {/* Filter Controls */}
            <div className="filter-controls">
              {/* Category Tabs and Dropdown */}
              <div className="filter-section">
                <div className="filter-tabs">
                  {displayedCategories.map((filter) => (
                    <button
                      key={filter}
                      className={`filter-tab ${
                        activeFilter === filter ? "active" : ""
                      }`}
                      onClick={() => setActiveFilter(filter)}
                    >
                      {filter}
                    </button>
                  ))}
                  {hiddenCategories.length > 0 && (
                    <div className="category-dropdown-container">
                      <button
                        className={`category-dropdown-button ${
                          hiddenCategories.includes(activeFilter)
                            ? "active"
                            : ""
                        }`}
                        onClick={toggleCategoryDropdown}
                      >
                        {hiddenCategories.includes(activeFilter)
                          ? activeFilter
                          : "More"}{" "}
                        <FaChevronDown />
                      </button>
                      {showCategoryDropdown && (
                        <div className="dropdown-menu">
                          {hiddenCategories.map((category) => (
                            <button
                              key={category}
                              className={
                                activeFilter === category ? "active" : ""
                              }
                              onClick={() => selectCategory(category)}
                            >
                              {category}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              {/* Price Range Dropdown */}
              <div className="range-dropdown-container">
                <button
                  className="range-dropdown-button"
                  onClick={toggleRangeDropdown}
                >
                  Range: {priceRange === "All" ? "All" : getPesoSigns(priceRange)} <FaChevronDown />
                </button>
                {showRangeDropdown && (
                  <div className="dropdown-menu">
                    <button onClick={() => selectPriceRange("All")}>
                      All Ranges
                    </button>
                    <button onClick={() => selectPriceRange("Low")}>
                      ₱ (Low Range)
                    </button>
                    <button onClick={() => selectPriceRange("Mid")}>
                      ₱₱ (Mid Range)
                    </button>
                    <button onClick={() => selectPriceRange("High")}>
                      ₱₱₱ (High Range)
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Dietary Preferences Indicator */}
            {userPreferences && (userPreferences.restrictions?.length > 0 || userPreferences.allergies?.length > 0) && (
              <div className="dietary-info-section">
                <div className="dietary-info-header">
                  <span>🍃 Active Dietary Filters:</span>
                  <button
                    className="edit-preferences-btn"
                    onClick={() => window.location.href = '/dietary-preferences'}
                  >
                    Edit Preferences
                  </button>
                </div>
                <div className="dietary-info-content">
                  {userPreferences.restrictions?.length > 0 && (
                    <div className="dietary-info-item">
                      <span className="dietary-label">Restrictions:</span>
                      <span className="dietary-values">{userPreferences.restrictions.join(', ')}</span>
                    </div>
                  )}
                  {userPreferences.allergies?.length > 0 && (
                    <div className="dietary-info-item">
                      <span className="dietary-label">Allergies:</span>
                      <span className="dietary-values">{userPreferences.allergies.join(', ')}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Personalized Recommendations */}
            {!searchTerm.trim() && (
              <RecommendedMeals onMealClick={openMealDetails} />
            )}

            {/* Notification */}
            {notification.show && (
              <div className="notification">{notification.message}</div>
            )}
            {/* Search loading/error */}
            {searchLoading && (
              <div className="search-loading">Loading search results...</div>
            )}
            {searchError && (
              <div className="search-error">{searchError}</div>
            )}
            <h2>
              {searchTerm.trim() ? "Search Results" : "Meals For You"}
            </h2>
            <div className="food-grid">
              {dishesToShow.length > 0 ? (
                dishesToShow.map((dish) => (
                  <div key={dish.id || dish._id} className="food-card">
                    <div className="food-card-image">
                      <img src={dish.image} alt={dish.name} />
                      <button
                        className="favorite-btn"
                        onClick={(e) => toggleFavorite(e, dish)}
                        title={
                          isFavorite(dish.id || dish._id)
                            ? "Remove from favorites"
                            : "Add to favorites"
                        }
                      >
                        {isFavorite(dish.id || dish._id) ? (
                          <FaHeart className="heart-icon filled" />
                        ) : (
                          <FaRegHeart className="heart-icon" />
                        )}
                      </button>
                    </div>
                    <div className="food-card-content">
                      <h3>{dish.name}</h3>
                      <div className="food-card-meta">
                        <div className="category-tag">
                          <span>{dish.category}</span>
                        </div>
                        <div className="rating">
                          <span>{dish.rating} &#9733;</span>
                        </div>
                      </div>
                      <div className="food-card-price">
                        <span className="price-range">
                          {getPesoSigns(dish.priceRange)}
                        </span>
                      </div>
                      <button
                        className="view-meal-btn"
                        onClick={() => openMealDetails(dish)}
                      >
                        View Meal
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-results">
                  <p>
                    {searchTerm.trim()
                      ? "No dishes found for your search."
                      : "No dishes found matching the selected filters."}
                  </p>
                </div>
              )}
            </div>

            {/* Meal Details Modal */}
            {showModal && selectedMeal && (
              <div className="modal-overlay">
                <div className="modal-content">
                  <div className="modal-header">
                    <h2>{selectedMeal.name}</h2>
                    <button className="close-modal" onClick={closeMealDetails}>
                      <FaTimes />
                    </button>
                  </div>
                  <div className="modal-body">
                    <div className="meal-image">
                      <img src={selectedMeal.image} alt={selectedMeal.name} />
                    </div>
                    <div className="meal-details">
                      <p className="meal-description">
                        {selectedMeal.description}
                      </p>
                      <div className="meal-meta">
                        <span className="meal-rating">
                          {selectedMeal.rating} &#9733;
                        </span>
                        <span className="meal-category">
                          {selectedMeal.category}
                        </span>
                        <span className="meal-price">
                          Calories: {selectedMeal.calories} (
                          {selectedMeal.priceRange} Range)
                        </span>
                      </div>
                          {selectedMeal.ingredients && selectedMeal.ingredients.length > 0 && (
                      <div className="meal-ingredients">
                        <h3>Ingredients</h3>
                          <ul className="ingredients-list">
                      {selectedMeal.ingredients.map((ingredient, idx) => (
                        <li key={idx}>{ingredient}</li>
                          ))}
                          </ul>
                      </div>
                          )}
                      <div className="meal-nutrition">
                        <h3>Nutrition Information</h3>
                        <div className="nutrition-grid">
                          <div className="nutrition-item">
                            <span className="nutrition-label">Calories:</span>
                            <span className="nutrition-value">
                              {selectedMeal.protein}g
                            </span>
                          </div>
                          <div className="nutrition-item">
                            <span className="nutrition-label">Carbs:</span>
                            <span className="nutrition-value">
                              {selectedMeal.carbs}g
                            </span>
                          </div>
                          <div className="nutrition-item">
                            <span className="nutrition-label">Fat:</span>
                            <span className="nutrition-value">
                              {selectedMeal.fat}g
                            </span>
                          </div>
                          <div className="nutrition-item">
                            <span className="nutrition-label">Prep Time:</span>
                            <span className="nutrition-value">
                              {selectedMeal.prepTime} mins
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="meal-steps">
                        <h3>Cooking Instructions</h3>
                        <ol>
                          {selectedMeal.instructions &&
                            selectedMeal.instructions.map((step, idx) => (
                              <li key={idx}>{step}</li>
                            ))}
                        </ol>
                      </div>
                      <div className="meal-tags">
                        <h3>Dietary Tags</h3>
                        <div className="tags-container">
                          {selectedMeal.dietaryTags &&
                            selectedMeal.dietaryTags.map((tag, idx) => (
                              <span key={idx} className="dietary-tag">
                                {tag}
                              </span>
                            ))}
                        </div>
                      </div>
                      <div className="meal-types">
                        <h3>Meal Types</h3>
                        <div className="tags-container">
                          {selectedMeal.mealType &&
                            selectedMeal.mealType.map((type, idx) => (
                              <span key={idx} className="meal-type-tag">
                                {type}
                              </span>
                            ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="modal-footer">
                    <button
                      className="favorite-modal-btn"
                      onClick={(e) => toggleFavorite(e, selectedMeal)}
                    >
                      {isFavorite(selectedMeal.id || selectedMeal._id) ? (
                        <>
                          Remove from Favorites <FaHeart />
                        </>
                      ) : (
                        <>
                          Add to Favorites <FaRegHeart />
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Floating Chat Button */}
      <FloatingChatButton />
    </div>
  );
};

export default Home;
